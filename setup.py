"""
Setup script for image2pdf package.
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Image to PDF converter tool"

setup(
    name="image2pdf",
    version="1.0.0",
    author="salikx",
    description="A command-line tool to convert images in a folder to PDF",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    packages=find_packages(),
    install_requires=[
        "Pillow>=8.0.0",
    ],
    entry_points={
        'console_scripts': [
            '2pdf=image2pdf.cli:main',
        ],
    },
    python_requires=">=3.6",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Graphics :: Graphics Conversion",
        "Topic :: Utilities",
    ],
    keywords="pdf image converter command-line tool",
    project_urls={
        "Source": "https://github.com/salikx/image2pdf",
        "Bug Reports": "https://github.com/salikx/image2pdf/issues",
    },
)
