# Image2PDF 安装和使用指南

## 🚀 快速开始

### 第一步：安装包

在项目根目录（包含setup.py的目录）下打开命令行，运行：

```bash
pip install .
```

如果您想要在开发模式下安装（可以修改代码）：

```bash
pip install -e .
```

### 第二步：验证安装

运行测试脚本验证安装是否成功：

```bash
python test_install.py
```

如果看到 "✓ 所有测试通过！" 说明安装成功。

### 第三步：开始使用

现在您可以在任何地方使用 `2pdf` 命令了！

```bash
# 基本用法
2pdf "C:\path\to\your\images"

# 指定输出文件
2pdf "C:\path\to\your\images" -o "my_book.pdf"
```

## 📖 详细使用说明

### 命令格式

```bash
2pdf <图片文件夹路径> [选项]
```

### 可用选项

- `-o, --output <路径>`: 指定输出PDF文件路径
- `-q, --quiet`: 静默模式，不显示详细信息
- `--help`: 显示帮助信息
- `--version`: 显示版本信息

### 使用示例

```bash
# 示例1：转换当前目录下的images文件夹
2pdf "./images"

# 示例2：转换指定路径的文件夹
2pdf "C:\Users\<USER>\Desktop\MyComics\Chapter1"

# 示例3：指定输出文件名和路径
2pdf "C:\path\to\images" -o "C:\output\my_comic.pdf"

# 示例4：静默模式转换
2pdf "C:\path\to\images" --quiet

# 示例5：查看帮助
2pdf --help
```

## 🔧 故障排除

### 常见问题

1. **命令未找到错误**
   ```
   '2pdf' 不是内部或外部命令
   ```
   **解决方案**: 重新安装包，确保使用 `pip install .`

2. **权限错误**
   ```
   Permission denied
   ```
   **解决方案**: 使用管理员权限运行命令行，或使用 `pip install --user .`

3. **路径包含空格**
   **解决方案**: 使用引号包围路径：`2pdf "C:\path with spaces\images"`

4. **图片格式不支持**
   **解决方案**: 确保图片是以下格式之一：PNG, JPG, JPEG, BMP, GIF, WebP

### 替代调用方式

如果 `2pdf` 命令不工作，您可以使用：

```bash
# 方式1：直接使用Python模块
python -m image2pdf "C:\path\to\images"

# 方式2：直接运行CLI脚本
python -c "from image2pdf.cli import main; main()" "C:\path\to\images"
```

## 🗑️ 卸载

如果需要卸载，运行：

```bash
pip uninstall image2pdf
```

## 💡 提示

1. **文件命名**: 为确保页面顺序正确，建议图片文件名使用数字前缀，如：`01.jpg`, `02.jpg`, `03.jpg`

2. **内存使用**: 处理大量图片时会占用较多内存，建议分批处理

3. **输出路径**: 如果不指定输出路径，PDF将保存在图片文件夹的同级目录，文件名为文件夹名称

4. **路径格式**: Windows用户可以使用正斜杠(/)或反斜杠(\)，建议使用引号包围包含空格的路径
