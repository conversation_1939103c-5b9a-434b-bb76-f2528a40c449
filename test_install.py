"""
测试脚本：验证image2pdf包是否正确安装和工作
"""

import os
import tempfile
from PIL import Image
import sys

def create_test_images(test_dir):
    """创建一些测试图片"""
    colors = [
        (255, 0, 0),    # 红色
        (0, 255, 0),    # 绿色
        (0, 0, 255),    # 蓝色
    ]
    
    image_paths = []
    for i, color in enumerate(colors, 1):
        # 创建一个简单的彩色图片
        img = Image.new('RGB', (200, 300), color)
        
        # 添加一些文字（页码）
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(img)
            draw.text((100, 150), f"Page {i}", fill=(255, 255, 255))
        except:
            pass  # 如果没有字体，跳过文字
        
        # 保存图片
        img_path = os.path.join(test_dir, f"page_{i:02d}.png")
        img.save(img_path)
        image_paths.append(img_path)
        print(f"创建测试图片: {img_path}")
    
    return image_paths

def test_core_functionality():
    """测试核心功能"""
    print("=== 测试核心功能 ===")
    
    try:
        from image2pdf.core import images_to_pdf
        print("✓ 成功导入 image2pdf.core")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    
    # 创建临时目录和测试图片
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建测试图片
        image_paths = create_test_images(temp_dir)
        
        # 测试转换
        output_path = os.path.join(temp_dir, "test_output.pdf")
        success = images_to_pdf(temp_dir, output_path, verbose=True)
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✓ PDF转换成功! 文件大小: {file_size} bytes")
            return True
        else:
            print("✗ PDF转换失败")
            return False

def test_cli_import():
    """测试命令行接口导入"""
    print("\n=== 测试命令行接口 ===")
    
    try:
        from image2pdf.cli import main, create_parser
        print("✓ 成功导入 image2pdf.cli")
        
        # 测试参数解析器
        parser = create_parser()
        print("✓ 参数解析器创建成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Image2PDF 安装测试")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查Pillow
    try:
        import PIL
        print(f"Pillow版本: {PIL.__version__}")
    except ImportError:
        print("✗ Pillow未安装")
        return
    
    # 测试核心功能
    core_test = test_core_functionality()
    
    # 测试CLI
    cli_test = test_cli_import()
    
    print("\n" + "=" * 50)
    if core_test and cli_test:
        print("✓ 所有测试通过！")
        print("\n现在您可以使用以下命令:")
        print("  2pdf \"path/to/your/images\"")
        print("  python -m image2pdf \"path/to/your/images\"")
    else:
        print("✗ 部分测试失败，请检查安装")

if __name__ == "__main__":
    main()
