import os
import time
from PIL import Image

def images_to_pdf(input_folder, output_path):
    """
    将指定文件夹中的图片按顺序合并为PDF文件
    
    Args:
        input_folder: 包含图片的文件夹路径
        output_path: 输出PDF文件的路径
    """
    start_time = time.time()
    
    # 获取所有图片文件
    image_files = []
    for file in os.listdir(input_folder):
        if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp')):
            image_files.append(os.path.join(input_folder, file))
    
    # 按文件名排序
    image_files.sort()
    
    if not image_files:
        print("未找到任何图片文件！")
        return
    
    # 打开第一张图片作为基础
    output = Image.open(image_files[0])
    if output.mode != "RGB":
        output = output.convert("RGB")
    
    # 准备其他图片
    sources = []
    for file in image_files[1:]:
        img = Image.open(file)
        if img.mode != "RGB":
            img = img.convert("RGB")
        sources.append(img)
    
    # 确保输出路径以.pdf结尾
    if not output_path.lower().endswith('.pdf'):
        output_path += '.pdf'
    
    # 保存PDF
    output.save(output_path, "PDF", save_all=True, append_images=sources)
    
    end_time = time.time()
    run_time = end_time - start_time
    print(f"转换完成！运行时间：{run_time:.2f} 秒")
    print(f"PDF文件已保存至：{output_path}")

if __name__ == "__main__":
    # 使用指定的源文件夹和输出路径
    input_folder = r"C:\Users\<USER>\Desktop\借来的玩具XXXXX! [猫岛汉化组] (C105) [ジャム理科 (まどかひふみ)] 借りたオモチャでxxxxxっ! (BanG Dream! It's MyGO!!!!!) [中国翻译]"
    folder_name = os.path.basename(input_folder)
    output_path = os.path.join(r"C:\Users\<USER>\Desktop", f"{folder_name}.pdf")
    
    if not os.path.exists(input_folder):
        print(f"错误：源文件夹 '{input_folder}' 不存在！")
    else:
        print(f"开始处理文件夹：{folder_name}")
        images_to_pdf(input_folder, output_path)
