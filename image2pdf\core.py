"""
Core functionality for converting images to PDF.
"""

import os
import time
from PIL import Image
from typing import List, Optional


def get_image_files(folder_path: str) -> List[str]:
    """
    获取文件夹中所有支持的图片文件
    
    Args:
        folder_path: 图片文件夹路径
        
    Returns:
        排序后的图片文件路径列表
    """
    supported_formats = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp')
    image_files = []
    
    for file in os.listdir(folder_path):
        if file.lower().endswith(supported_formats):
            image_files.append(os.path.join(folder_path, file))
    
    # 按文件名排序
    image_files.sort()
    return image_files


def images_to_pdf(input_folder: str, output_path: Optional[str] = None, verbose: bool = True) -> bool:
    """
    将指定文件夹中的图片按顺序合并为PDF文件
    
    Args:
        input_folder: 包含图片的文件夹路径
        output_path: 输出PDF文件的路径，如果为None则自动生成
        verbose: 是否显示详细信息
        
    Returns:
        转换是否成功
    """
    if verbose:
        start_time = time.time()
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        if verbose:
            print(f"错误：源文件夹 '{input_folder}' 不存在！")
        return False
    
    if not os.path.isdir(input_folder):
        if verbose:
            print(f"错误：'{input_folder}' 不是一个文件夹！")
        return False
    
    # 获取所有图片文件
    image_files = get_image_files(input_folder)
    
    if not image_files:
        if verbose:
            print("未找到任何支持的图片文件！")
            print("支持的格式：PNG, JPG, JPEG, BMP, GIF, WebP")
        return False
    
    # 生成输出路径
    if output_path is None:
        folder_name = os.path.basename(input_folder.rstrip(os.sep))
        output_path = os.path.join(os.path.dirname(input_folder), f"{folder_name}.pdf")
    
    # 确保输出路径以.pdf结尾
    if not output_path.lower().endswith('.pdf'):
        output_path += '.pdf'
    
    try:
        if verbose:
            print(f"找到 {len(image_files)} 张图片")
            print("开始转换...")
        
        # 打开第一张图片作为基础
        output = Image.open(image_files[0])
        if output.mode != "RGB":
            output = output.convert("RGB")
        
        # 准备其他图片
        sources = []
        for i, file in enumerate(image_files[1:], 1):
            if verbose and len(image_files) > 10 and i % 10 == 0:
                print(f"处理进度: {i}/{len(image_files)-1}")
            
            img = Image.open(file)
            if img.mode != "RGB":
                img = img.convert("RGB")
            sources.append(img)
        
        # 保存PDF
        output.save(output_path, "PDF", save_all=True, append_images=sources)
        
        if verbose:
            end_time = time.time()
            run_time = end_time - start_time
            print(f"转换完成！运行时间：{run_time:.2f} 秒")
            print(f"PDF文件已保存至：{output_path}")
        
        return True
        
    except Exception as e:
        if verbose:
            print(f"转换过程中发生错误：{str(e)}")
        return False
