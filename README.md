# Image2PDF - 图片转PDF命令行工具

一个简单易用的命令行工具，可以将文件夹中的图片按顺序合并为单个PDF文件。

## 功能特点

- 🖼️ 支持多种图片格式：PNG, JPG, JPEG, BMP, GIF, WebP
- 📄 自动按文件名排序确保页面顺序正确
- ⚡ 简单的命令行接口，一行命令完成转换
- 🎯 智能输出路径生成
- 📊 转换进度显示和时间统计

## 安装方法

### 方法一：本地安装（推荐）

在项目根目录下运行：

```bash
pip install .
```

### 方法二：开发模式安装

如果您想要修改代码，可以使用开发模式安装：

```bash
pip install -e .
```

## 使用方法

安装完成后，您可以在任何地方使用 `2pdf` 命令：

### 基本用法

```bash
# 转换图片文件夹为PDF（使用默认输出路径）
2pdf "C:\path\to\images"

# 指定输出文件路径
2pdf "C:\path\to\images" -o "output.pdf"

# 静默模式（不显示详细信息）
2pdf "C:\path\to\images" --quiet
```

### 使用示例

```bash
# 示例1：转换桌面上的图片文件夹
2pdf "C:\Users\<USER>\Desktop\MyImages"

# 示例2：指定输出到特定位置
2pdf "C:\Users\<USER>\Desktop\MyImages" -o "C:\Users\<USER>\Documents\MyBook.pdf"

# 示例3：查看帮助信息
2pdf --help

# 示例4：查看版本信息
2pdf --version
```

### 替代调用方式

您也可以使用以下方式调用：

```bash
# 使用Python模块方式
python -m image2pdf "C:\path\to\images"
```

## 命令行参数

- `input_folder`: 包含图片的文件夹路径（必需）
- `-o, --output`: 输出PDF文件路径（可选）
- `-q, --quiet`: 静默模式，不显示详细信息
- `--help`: 显示帮助信息
- `--version`: 显示版本信息

## 注意事项

- ⚠️ **内存使用**：处理大量图片时会消耗较多内存，建议超过150张图片时确保有足够内存（32GB+）
- 📁 **文件夹结构**：确保图片文件夹存在且包含支持的图片格式
- 🔤 **文件排序**：图片将按文件名字母顺序排序，建议使用数字前缀确保正确顺序

## 感谢以下项目

[![Readme Card](https://github-readme-stats.vercel.app/api/pin/?username=hect0x7&repo=JMComic-Crawler-Python)](https://github.com/hect0x7/JMComic-Crawler-Python)

## 后续优化计划

- 每话生成单独的PDF，最后合并，从而减轻内存消耗
- 添加图片压缩选项
- 支持更多输出格式
