"""
Command line interface for image2pdf.
"""

import argparse
import sys
import os
from .core import images_to_pdf


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        prog='2pdf',
        description='将文件夹中的图片转换为PDF文件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  2pdf "C:\\path\\to\\images"                    # 使用默认输出路径
  2pdf "C:\\path\\to\\images" -o output.pdf     # 指定输出文件
  2pdf "C:\\path\\to\\images" --quiet           # 静默模式
  
支持的图片格式: PNG, JPG, JPEG, BMP, GIF, WebP
        """
    )
    
    parser.add_argument(
        'input_folder',
        help='包含图片的文件夹路径'
    )
    
    parser.add_argument(
        '-o', '--output',
        dest='output_path',
        help='输出PDF文件路径（可选，默认使用文件夹名称）'
    )
    
    parser.add_argument(
        '-q', '--quiet',
        action='store_true',
        help='静默模式，不显示详细信息'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='%(prog)s 1.0.0'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 处理路径
    input_folder = os.path.abspath(args.input_folder)
    output_path = None
    if args.output_path:
        output_path = os.path.abspath(args.output_path)
    
    # 执行转换
    verbose = not args.quiet
    success = images_to_pdf(input_folder, output_path, verbose)
    
    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
